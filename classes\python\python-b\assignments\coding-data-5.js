// <PERSON><PERSON> liệu bài tập luyện tập code - Bài 5: Logic và Quyết Đ<PERSON>nh
const codingProblems = [
    {
        id: 1,
        title: "So Sánh Bằng Nhau",
        difficulty: "Dễ",
        filename: "so_sanh_bang_nhau.py",
        description: "Viết chương trình nhận vào hai số nguyên a và b, sau đó in ra True nếu chúng bằng nhau, ngư<PERSON><PERSON> lại in ra False.",
        objective: "Luyện tập toán tử ==.",
        sampleCode: `a = int(input("Nhap so a: "))
b = int(input("Nhap so b: "))

ket_qua = (a == b)
print(ket_qua)`,
        testcases: [
            { input: ["10", "10"], expectedOutput: "True" },
            { input: ["10", "15"], expectedOutput: "False" },
            { input: ["5", "5"], expectedOutput: "True" },
            { input: ["0", "0"], expectedOutput: "True" },
            { input: ["100", "200"], expectedOutput: "False" },
            { input: ["-5", "-5"], expectedOutput: "True" },
            { input: ["7", "8"], expectedOutput: "False" },
            { input: ["999", "999"], expectedOutput: "True" },
            { input: ["1", "2"], expectedOutput: "False" },
            { input: ["50", "50"], expectedOutput: "True" }
        ]
    },
    {
        id: 2,
        title: "Kiểm Tra Tuổi Xem Phim",
        difficulty: "Dễ",
        filename: "kiem_tra_tuoi_xem_phim.py",
        description: "Rạp chiếu phim yêu cầu khán giả phải từ 13 tuổi trở lên để xem một bộ phim. Viết chương trình nhận vào tuổi của một người, sau đó in ra True nếu họ đủ tuổi, ngược lại in ra False.",
        objective: "Luyện tập toán tử >=.",
        sampleCode: `tuoi = int(input("Nhap tuoi cua ban: "))

du_tuoi = (tuoi >= 13)

print("Du tuoi xem phim:", du_tuoi)`,
        testcases: [
            { input: ["15"], expectedOutput: "Du tuoi xem phim: True" },
            { input: ["12"], expectedOutput: "Du tuoi xem phim: False" },
            { input: ["13"], expectedOutput: "Du tuoi xem phim: True" },
            { input: ["18"], expectedOutput: "Du tuoi xem phim: True" },
            { input: ["10"], expectedOutput: "Du tuoi xem phim: False" },
            { input: ["25"], expectedOutput: "Du tuoi xem phim: True" },
            { input: ["8"], expectedOutput: "Du tuoi xem phim: False" },
            { input: ["14"], expectedOutput: "Du tuoi xem phim: True" },
            { input: ["11"], expectedOutput: "Du tuoi xem phim: False" },
            { input: ["20"], expectedOutput: "Du tuoi xem phim: True" }
        ]
    },
    {
        id: 3,
        title: "So Sánh Chuỗi",
        difficulty: "Dễ",
        filename: "so_sanh_chuoi.py",
        description: "Viết chương trình nhận vào hai chuỗi s1 và s2. In ra True nếu chúng giống hệt nhau (phân biệt chữ hoa/thường), ngược lại in ra False.",
        objective: "Hiểu rằng so sánh chuỗi có phân biệt chữ hoa/thường.",
        sampleCode: `s1 = input("Nhap chuoi s1: ")
s2 = input("Nhap chuoi s2: ")

ket_qua = (s1 == s2)

print("Hai chuoi giong het nhau:", ket_qua)`,
        testcases: [
            { input: ["python", "python"], expectedOutput: "Hai chuoi giong het nhau: True" },
            { input: ["Python", "python"], expectedOutput: "Hai chuoi giong het nhau: False" },
            { input: ["hello", "hello"], expectedOutput: "Hai chuoi giong het nhau: True" },
            { input: ["Hello", "hello"], expectedOutput: "Hai chuoi giong het nhau: False" },
            { input: ["abc", "abc"], expectedOutput: "Hai chuoi giong het nhau: True" },
            { input: ["ABC", "abc"], expectedOutput: "Hai chuoi giong het nhau: False" },
            { input: ["test", "Test"], expectedOutput: "Hai chuoi giong het nhau: False" },
            { input: ["same", "same"], expectedOutput: "Hai chuoi giong het nhau: True" },
            { input: ["Different", "different"], expectedOutput: "Hai chuoi giong het nhau: False" },
            { input: ["equal", "equal"], expectedOutput: "Hai chuoi giong het nhau: True" }
        ]
    },
    {
        id: 4,
        title: "Logic \"VÀ\" (and)",
        difficulty: "Dễ",
        filename: "logic_va.py",
        description: "Để qua môn, một sinh viên cần có điểm thi cuối kỳ lớn hơn hoặc bằng 5 VÀ điểm chuyên cần lớn hơn hoặc bằng 7. Viết chương trình nhận vào hai điểm này, in ra True nếu sinh viên qua môn, ngược lại in ra False.",
        objective: "Luyện tập toán tử and.",
        sampleCode: `diem_thi = float(input("Nhap diem thi cuoi ky: "))
diem_chuyen_can = float(input("Nhap diem chuyen can: "))

dieu_kien_1 = (diem_thi >= 5)
dieu_kien_2 = (diem_chuyen_can >= 7)

ket_qua_cuoi_cung = dieu_kien_1 and dieu_kien_2

print("Sinh vien qua mon:", ket_qua_cuoi_cung)`,
        testcases: [
            { input: ["8", "9"], expectedOutput: "Sinh vien qua mon: True" },
            { input: ["9", "6"], expectedOutput: "Sinh vien qua mon: False" },
            { input: ["5", "7"], expectedOutput: "Sinh vien qua mon: True" },
            { input: ["4", "8"], expectedOutput: "Sinh vien qua mon: False" },
            { input: ["6", "8"], expectedOutput: "Sinh vien qua mon: True" },
            { input: ["3", "9"], expectedOutput: "Sinh vien qua mon: False" },
            { input: ["7", "5"], expectedOutput: "Sinh vien qua mon: False" },
            { input: ["10", "10"], expectedOutput: "Sinh vien qua mon: True" },
            { input: ["2", "3"], expectedOutput: "Sinh vien qua mon: False" },
            { input: ["5.5", "7.5"], expectedOutput: "Sinh vien qua mon: True" }
        ]
    },
    {
        id: 5,
        title: "Phủ Định (not)",
        difficulty: "Dễ",
        filename: "phu_dinh.py",
        description: "Viết chương trình nhận vào nhiệt độ hiện tại. In ra True nếu nhiệt độ đó KHÔNG LỚN HƠN 30 độ, ngược lại in ra False.",
        objective: "Luyện tập toán tử not.",
        sampleCode: `nhiet_do = float(input("Nhap nhiet do hien tai: "))

la_qua_nong = (nhiet_do > 30)
khong_qua_nong = not la_qua_nong

print("Nhiet do khong qua nong:", khong_qua_nong)`,
        testcases: [
            { input: ["28"], expectedOutput: "Nhiet do khong qua nong: True" },
            { input: ["32"], expectedOutput: "Nhiet do khong qua nong: False" },
            { input: ["30"], expectedOutput: "Nhiet do khong qua nong: True" },
            { input: ["35"], expectedOutput: "Nhiet do khong qua nong: False" },
            { input: ["25"], expectedOutput: "Nhiet do khong qua nong: True" },
            { input: ["40"], expectedOutput: "Nhiet do khong qua nong: False" },
            { input: ["20"], expectedOutput: "Nhiet do khong qua nong: True" },
            { input: ["31"], expectedOutput: "Nhiet do khong qua nong: False" },
            { input: ["29"], expectedOutput: "Nhiet do khong qua nong: True" },
            { input: ["33"], expectedOutput: "Nhiet do khong qua nong: False" }
        ]
    },
    {
        id: 6,
        title: "Kiểm Tra Khoảng Giá Trị",
        difficulty: "Trung Bình",
        filename: "kiem_tra_khoang.py",
        description: "Một bài kiểm tra hợp lệ có điểm từ 0 đến 10. Viết chương trình nhận vào một số điểm, in ra True nếu điểm đó hợp lệ (lớn hơn hoặc bằng 0 VÀ nhỏ hơn hoặc bằng 10), ngược lại in ra False.",
        objective: "Kết hợp and, >= và <=.",
        sampleCode: `diem = float(input("Nhap diem: "))

dieu_kien_lon_hon_0 = (diem >= 0)
dieu_kien_nho_hon_10 = (diem <= 10)

diem_hop_le = dieu_kien_lon_hon_0 and dieu_kien_nho_hon_10

print("Diem hop le:", diem_hop_le)`,
        testcases: [
            { input: ["7.5"], expectedOutput: "Diem hop le: True" },
            { input: ["11"], expectedOutput: "Diem hop le: False" },
            { input: ["0"], expectedOutput: "Diem hop le: True" },
            { input: ["10"], expectedOutput: "Diem hop le: True" },
            { input: ["-1"], expectedOutput: "Diem hop le: False" },
            { input: ["5"], expectedOutput: "Diem hop le: True" },
            { input: ["15"], expectedOutput: "Diem hop le: False" },
            { input: ["8.5"], expectedOutput: "Diem hop le: True" },
            { input: ["-5"], expectedOutput: "Diem hop le: False" },
            { input: ["9.9"], expectedOutput: "Diem hop le: True" }
        ]
    },
    {
        id: 7,
        title: "Điều Kiện Giảm Giá",
        difficulty: "Trung Bình",
        filename: "dieu_kien_giam_gia.py",
        description: "Cửa hàng sẽ giảm giá cho khách hàng nếu hóa đơn của họ trên 500,000 VND HOẶC nếu hôm nay là Chủ Nhật. Viết chương trình nhận vào tổng tiền hóa đơn và một chuỗi cho biết ngày trong tuần. In ra True nếu khách hàng được giảm giá, ngược lại in ra False.",
        objective: "Luyện tập toán tử or.",
        sampleCode: `tong_tien = float(input("Nhap tong tien hoa don: "))
ngay_trong_tuan = input("Hom nay la thu may? ")

dieu_kien_tien = (tong_tien > 500000)
dieu_kien_ngay = (ngay_trong_tuan == "Chu Nhat")

duoc_giam_gia = dieu_kien_tien or dieu_kien_ngay

print("Khach hang duoc giam gia:", duoc_giam_gia)`,
        testcases: [
            { input: ["600000", "Thu Ba"], expectedOutput: "Khach hang duoc giam gia: True" },
            { input: ["100000", "Chu Nhat"], expectedOutput: "Khach hang duoc giam gia: True" },
            { input: ["200000", "Thu Tu"], expectedOutput: "Khach hang duoc giam gia: False" },
            { input: ["700000", "Thu Hai"], expectedOutput: "Khach hang duoc giam gia: True" },
            { input: ["300000", "Chu Nhat"], expectedOutput: "Khach hang duoc giam gia: True" },
            { input: ["400000", "Thu Nam"], expectedOutput: "Khach hang duoc giam gia: False" },
            { input: ["800000", "Thu Sau"], expectedOutput: "Khach hang duoc giam gia: True" },
            { input: ["150000", "Thu Bay"], expectedOutput: "Khach hang duoc giam gia: False" },
            { input: ["550000", "Thu Hai"], expectedOutput: "Khach hang duoc giam gia: True" },
            { input: ["50000", "Chu Nhat"], expectedOutput: "Khach hang duoc giam gia: True" }
        ]
    },
    {
        id: 8,
        title: "Kiểm Tra Số Chẵn",
        difficulty: "Trung Bình",
        filename: "kiem_tra_so_chan.py",
        description: "Viết chương trình nhận vào một số nguyên, in ra True nếu đó là số chẵn, ngược lại in ra False. Gợi ý: một số là chẵn nếu nó chia hết cho 2 (phần dư bằng 0).",
        objective: "Kết hợp toán tử số học (%) và toán tử so sánh (==).",
        sampleCode: `so = int(input("Nhap mot so nguyen: "))

ket_qua = (so % 2 == 0)

print("La so chan:", ket_qua)`,
        testcases: [
            { input: ["10"], expectedOutput: "La so chan: True" },
            { input: ["7"], expectedOutput: "La so chan: False" },
            { input: ["0"], expectedOutput: "La so chan: True" },
            { input: ["1"], expectedOutput: "La so chan: False" },
            { input: ["100"], expectedOutput: "La so chan: True" },
            { input: ["99"], expectedOutput: "La so chan: False" },
            { input: ["2"], expectedOutput: "La so chan: True" },
            { input: ["3"], expectedOutput: "La so chan: False" },
            { input: ["50"], expectedOutput: "La so chan: True" },
            { input: ["51"], expectedOutput: "La so chan: False" }
        ]
    },
    {
        id: 9,
        title: "Mật Khẩu Hợp Lệ",
        difficulty: "Trung Bình",
        filename: "mat_khau_hop_le.py",
        description: "Một mật khẩu được coi là hợp lệ nếu nó có độ dài từ 8 ký tự trở lên. Viết chương trình nhận vào một chuỗi mật khẩu, in ra True nếu nó hợp lệ, ngược lại in ra False.",
        objective: "Kết hợp hàm len() và toán tử so sánh.",
        sampleCode: `mat_khau = input("Nhap mat khau: ")

do_dai = len(mat_khau)
ket_qua = (do_dai >= 8)

print("Mat khau hop le:", ket_qua)`,
        testcases: [
            { input: ["password123"], expectedOutput: "Mat khau hop le: True" },
            { input: ["abc"], expectedOutput: "Mat khau hop le: False" },
            { input: ["12345678"], expectedOutput: "Mat khau hop le: True" },
            { input: ["short"], expectedOutput: "Mat khau hop le: False" },
            { input: ["verylongpassword"], expectedOutput: "Mat khau hop le: True" },
            { input: ["1234567"], expectedOutput: "Mat khau hop le: False" },
            { input: ["abcdefgh"], expectedOutput: "Mat khau hop le: True" },
            { input: ["test"], expectedOutput: "Mat khau hop le: False" },
            { input: ["mypassword"], expectedOutput: "Mat khau hop le: True" },
            { input: ["hello"], expectedOutput: "Mat khau hop le: False" }
        ]
    },
    {
        id: 10,
        title: "So Sánh Độ Dài Chuỗi",
        difficulty: "Trung Bình",
        filename: "so_sanh_do_dai_chuoi.py",
        description: "Viết chương trình nhận vào hai chuỗi s1 và s2. In ra True nếu độ dài của s1 lớn hơn độ dài của s2, ngược lại in ra False.",
        objective: "Vận dụng len() và toán tử >.",
        sampleCode: `s1 = input("Nhap chuoi s1: ")
s2 = input("Nhap chuoi s2: ")

do_dai_s1 = len(s1)
do_dai_s2 = len(s2)

ket_qua = (do_dai_s1 > do_dai_s2)

print("s1 dai hon s2:", ket_qua)`,
        testcases: [
            { input: ["Python", "Java"], expectedOutput: "s1 dai hon s2: True" },
            { input: ["Hi", "Hello"], expectedOutput: "s1 dai hon s2: False" },
            { input: ["abc", "ab"], expectedOutput: "s1 dai hon s2: True" },
            { input: ["test", "testing"], expectedOutput: "s1 dai hon s2: False" },
            { input: ["long", "short"], expectedOutput: "s1 dai hon s2: False" },
            { input: ["programming", "code"], expectedOutput: "s1 dai hon s2: True" },
            { input: ["a", "abc"], expectedOutput: "s1 dai hon s2: False" },
            { input: ["hello", "hi"], expectedOutput: "s1 dai hon s2: True" },
            { input: ["same", "same"], expectedOutput: "s1 dai hon s2: False" },
            { input: ["longer", "short"], expectedOutput: "s1 dai hon s2: True" }
        ]
    },
    {
        id: 11,
        title: "Điều Kiện Nhận Học Bổng",
        difficulty: "Khó",
        filename: "dieu_kien_hoc_bong.py",
        description: "Để nhận học bổng, sinh viên phải có Điểm trung bình (GPA) từ 8.5 trở lên VÀ (không có môn nào dưới 6.5 HOẶC có thành tích hoạt động ngoại khóa xuất sắc). Viết chương trình nhận vào GPA, điểm môn thấp nhất, và một chuỗi cho biết thành tích ngoại khóa. In ra True nếu đủ điều kiện, ngược lại in ra False.",
        objective: "Kết hợp and, or và dùng dấu ngoặc đơn để đảm bảo thứ tự ưu tiên.",
        sampleCode: `gpa = float(input("Nhap GPA: "))
diem_thap_nhat = float(input("Nhap diem mon thap nhat: "))
ngoai_khoa = input("Thanh tich ngoai khoa (Xuat sac/Khong): ")

dieu_kien_gpa = (gpa >= 8.5)

dieu_kien_diem_thanh_phan = (diem_thap_nhat >= 6.5)
dieu_kien_ngoai_khoa = (ngoai_khoa == "Xuat sac")

dieu_kien_phu = dieu_kien_diem_thanh_phan or dieu_kien_ngoai_khoa

ket_qua_cuoi_cung = dieu_kien_gpa and dieu_kien_phu

print("Du dieu kien hoc bong:", ket_qua_cuoi_cung)`,
        testcases: [
            { input: ["8.6", "7.0", "Khong"], expectedOutput: "Du dieu kien hoc bong: True" },
            { input: ["8.7", "6.0", "Xuat sac"], expectedOutput: "Du dieu kien hoc bong: True" },
            { input: ["9.0", "6.0", "Khong"], expectedOutput: "Du dieu kien hoc bong: False" },
            { input: ["8.5", "6.5", "Khong"], expectedOutput: "Du dieu kien hoc bong: True" },
            { input: ["8.0", "7.0", "Xuat sac"], expectedOutput: "Du dieu kien hoc bong: False" },
            { input: ["9.0", "5.0", "Xuat sac"], expectedOutput: "Du dieu kien hoc bong: True" },
            { input: ["8.5", "5.5", "Khong"], expectedOutput: "Du dieu kien hoc bong: False" },
            { input: ["8.8", "8.0", "Khong"], expectedOutput: "Du dieu kien hoc bong: True" },
            { input: ["8.2", "6.0", "Khong"], expectedOutput: "Du dieu kien hoc bong: False" },
            { input: ["8.9", "6.2", "Xuat sac"], expectedOutput: "Du dieu kien hoc bong: True" }
        ]
    },
    {
        id: 12,
        title: "Kiểm Tra Năm Nhuận",
        difficulty: "Khó",
        filename: "kiem_tra_nam_nhuan.py",
        description: "Viết chương trình nhận vào một số năm, in ra True nếu đó là năm nhuận, ngược lại in ra False. Quy tắc xác định năm nhuận: (chia hết cho 4 VÀ không chia hết cho 100) HOẶC chia hết cho 400.",
        objective: "Vận dụng logic phức tạp với and, or, not và %.",
        sampleCode: `nam = int(input("Nhap nam: "))

chia_het_cho_4 = (nam % 4 == 0)
khong_chia_het_cho_100 = (nam % 100 != 0)
dieu_kien_1 = chia_het_cho_4 and khong_chia_het_cho_100

dieu_kien_2 = (nam % 400 == 0)

la_nam_nhuan = dieu_kien_1 or dieu_kien_2

print("La nam nhuan:", la_nam_nhuan)`,
        testcases: [
            { input: ["2000"], expectedOutput: "La nam nhuan: True" },
            { input: ["2024"], expectedOutput: "La nam nhuan: True" },
            { input: ["1900"], expectedOutput: "La nam nhuan: False" },
            { input: ["2004"], expectedOutput: "La nam nhuan: True" },
            { input: ["1800"], expectedOutput: "La nam nhuan: False" },
            { input: ["2400"], expectedOutput: "La nam nhuan: True" },
            { input: ["2001"], expectedOutput: "La nam nhuan: False" },
            { input: ["2100"], expectedOutput: "La nam nhuan: False" },
            { input: ["2020"], expectedOutput: "La nam nhuan: True" },
            { input: ["1999"], expectedOutput: "La nam nhuan: False" }
        ]
    },
    {
        id: 13,
        title: "Kiểm Tra Số Âm Lẻ",
        difficulty: "Khó",
        filename: "kiem_tra_so_am_le.py",
        description: "Viết chương trình nhận vào một số nguyên, in ra True nếu số đó vừa là số âm VÀ vừa là số lẻ. Ngược lại in ra False.",
        objective: "Kết hợp kiểm tra dấu và tính chẵn lẻ.",
        sampleCode: `so = int(input("Nhap mot so nguyen: "))

la_so_am = (so < 0)
la_so_le = (so % 2 != 0)

ket_qua = la_so_am and la_so_le

print("La so am le:", ket_qua)`,
        testcases: [
            { input: ["-7"], expectedOutput: "La so am le: True" },
            { input: ["-6"], expectedOutput: "La so am le: False" },
            { input: ["7"], expectedOutput: "La so am le: False" },
            { input: ["-1"], expectedOutput: "La so am le: True" },
            { input: ["0"], expectedOutput: "La so am le: False" },
            { input: ["-2"], expectedOutput: "La so am le: False" },
            { input: ["5"], expectedOutput: "La so am le: False" },
            { input: ["-9"], expectedOutput: "La so am le: True" },
            { input: ["-4"], expectedOutput: "La so am le: False" },
            { input: ["3"], expectedOutput: "La so am le: False" }
        ]
    },
    {
        id: 14,
        title: "Xác Thực Đăng Nhập",
        difficulty: "Khó",
        filename: "xac_thuc_dang_nhap.py",
        description: "Chương trình có sẵn một tên đăng nhập và mật khẩu đúng. Viết chương trình nhận vào tên đăng nhập và mật khẩu từ người dùng. In ra True nếu cả hai đều khớp, ngược lại in ra False.",
        objective: "Vận dụng == và and trong bài toán thực tế.",
        sampleCode: `USERNAME_DUNG = "admin"
PASSWORD_DUNG = "123456"

username_nhap = input("Nhap ten dang nhap: ")
password_nhap = input("Nhap mat khau: ")

khop_username = (username_nhap == USERNAME_DUNG)
khop_password = (password_nhap == PASSWORD_DUNG)

dang_nhap_thanh_cong = khop_username and khop_password

print("Dang nhap thanh cong:", dang_nhap_thanh_cong)`,
        testcases: [
            { input: ["admin", "123456"], expectedOutput: "Dang nhap thanh cong: True" },
            { input: ["admin", "wrong"], expectedOutput: "Dang nhap thanh cong: False" },
            { input: ["user", "123456"], expectedOutput: "Dang nhap thanh cong: False" },
            { input: ["Admin", "123456"], expectedOutput: "Dang nhap thanh cong: False" },
            { input: ["admin", "12345"], expectedOutput: "Dang nhap thanh cong: False" },
            { input: ["test", "test"], expectedOutput: "Dang nhap thanh cong: False" },
            { input: ["admin", "password"], expectedOutput: "Dang nhap thanh cong: False" },
            { input: ["root", "123456"], expectedOutput: "Dang nhap thanh cong: False" },
            { input: ["admin", ""], expectedOutput: "Dang nhap thanh cong: False" },
            { input: ["", "123456"], expectedOutput: "Dang nhap thanh cong: False" }
        ]
    }
];

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = codingProblems;
}
