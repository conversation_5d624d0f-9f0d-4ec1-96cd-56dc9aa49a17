// Bộ câu hỏi trắc nghiệm - Bài 5: Logic và Quyết Định
const quizData = [
    // MỨC ĐỘ DỄ (Nhận biết & Thông hiểu cơ bản)
    {
        question: "Kiểu dữ liệu Boolean trong Python có thể nhận những giá trị nào?",
        options: ["0 và 1", "\"Yes\" và \"No\"", "True và False", "T và F"],
        correct: 2,
        explanation: "Kiểu dữ liệu Boolean trong Python chỉ có hai giá trị: True và False (viết hoa chữ cái đầu)."
    },
    {
        question: "Toán tử nào được sử dụng để so sánh \"bằng\"?",
        options: ["=", "==", "is", "eq"],
        correct: 1,
        explanation: "Toán tử == được sử dụng để so sánh bằng. Dấu = là toán tử gán, không phải so sánh."
    },
    {
        question: "Toán tử nào được sử dụng để so sánh \"không bằng\"?",
        options: ["!=", "<>", "ne", "not =="],
        correct: 0,
        explanation: "Toán tử != được sử dụng để so sánh không bằng trong Python."
    },
    {
        question: "Toán tử nào được sử dụng để so sánh \"lớn hơn\"?",
        options: ["=>", ">", ">>", "gt"],
        correct: 1,
        explanation: "Toán tử > được sử dụng để so sánh lớn hơn. => không phải toán tử Python."
    },
    {
        question: "Toán tử nào được sử dụng để so sánh \"nhỏ hơn hoặc bằng\"?",
        options: ["=<", "/<", "<-", "<="],
        correct: 3,
        explanation: "Toán tử <= được sử dụng để so sánh nhỏ hơn hoặc bằng."
    },
    {
        question: "Toán tử logic and có nghĩa là gì?",
        options: ["Hoặc", "Và", "Không", "Bằng"],
        correct: 1,
        explanation: "Toán tử logic 'and' có nghĩa là 'và', chỉ trả về True khi cả hai vế đều True."
    },
    {
        question: "Toán tử logic or có nghĩa là gì?",
        options: ["Hoặc", "Và", "Không", "Bằng"],
        correct: 0,
        explanation: "Toán tử logic 'or' có nghĩa là 'hoặc', trả về True khi ít nhất một vế là True."
    },
    {
        question: "Toán tử logic not có nghĩa là gì?",
        options: ["Hoặc", "Và", "Phủ định (Không)", "Bằng"],
        correct: 2,
        explanation: "Toán tử logic 'not' có nghĩa là phủ định, đảo ngược giá trị Boolean."
    },
    {
        question: "Kết quả của 10 > 5 là gì?",
        options: ["True", "False", "1", "Lỗi"],
        correct: 0,
        explanation: "10 > 5 là một biểu thức so sánh đúng, nên kết quả là True."
    },
    {
        question: "Kết quả của 5 == 5 là gì?",
        options: ["True", "False", "5", "Lỗi"],
        correct: 0,
        explanation: "5 == 5 là một biểu thức so sánh đúng (5 bằng 5), nên kết quả là True."
    },
    {
        question: "Kết quả của \"hello\" == \"Hello\" là gì?",
        options: ["True", "False", "None", "Lỗi"],
        correct: 1,
        explanation: "Python phân biệt chữ hoa và chữ thường, nên \"hello\" không bằng \"Hello\", kết quả là False."
    },
    {
        question: "Kết quả của not True là gì?",
        options: ["True", "False", "1", "0"],
        correct: 1,
        explanation: "Toán tử 'not' đảo ngược giá trị Boolean, nên not True = False."
    },
    {
        question: "Kết quả của True and True là gì?",
        options: ["True", "False", "2", "Lỗi"],
        correct: 0,
        explanation: "Toán tử 'and' trả về True khi cả hai vế đều True, nên True and True = True."
    },
    {
        question: "Kết quả của True or False là gì?",
        options: ["True", "False", "0", "Lỗi"],
        correct: 0,
        explanation: "Toán tử 'or' trả về True khi ít nhất một vế là True, nên True or False = True."
    },
    {
        question: "Biểu thức x = 5 và x == 5 khác nhau ở điểm nào?",
        options: ["x = 5 là so sánh, x == 5 là gán giá trị", "x = 5 là gán giá trị, x == 5 là so sánh", "Chúng hoàn toàn giống nhau", "x = 5 gây lỗi, x == 5 thì không"],
        correct: 1,
        explanation: "x = 5 là phép gán giá trị 5 cho biến x, còn x == 5 là phép so sánh kiểm tra x có bằng 5 không."
    },
    // MỨC ĐỘ TRUNG BÌNH (Thông hiểu & Vận dụng thấp)
    {
        question: "Kết quả của 100 != 100 là gì?",
        options: ["True", "False", "0", "None"],
        correct: 1,
        explanation: "100 != 100 nghĩa là '100 không bằng 100', điều này sai nên kết quả là False."
    },
    {
        question: "Kết quả của 50 >= 50 là gì?",
        options: ["True", "False", "50", "Lỗi"],
        correct: 0,
        explanation: "50 >= 50 nghĩa là '50 lớn hơn hoặc bằng 50', điều này đúng nên kết quả là True."
    },
    {
        question: "Kết quả của type(True) là gì?",
        options: ["<class 'boolean'>", "<class 'int'>", "<class 'bool'>", "<class 'str'>"],
        correct: 2,
        explanation: "Kiểu dữ liệu của True trong Python là bool, nên type(True) trả về <class 'bool'>."
    },
    {
        question: "Kết quả của True and False là gì?",
        options: ["True", "False", "1", "Lỗi"],
        correct: 1,
        explanation: "Toán tử 'and' chỉ trả về True khi cả hai vế đều True. True and False = False."
    },
    {
        question: "Kết quả của False or False là gì?",
        options: ["True", "False", "0", "Lỗi"],
        correct: 1,
        explanation: "Toán tử 'or' trả về True khi ít nhất một vế là True. False or False = False."
    },
    {
        question: "Kết quả của not False là gì?",
        options: ["True", "False", "1", "0"],
        correct: 0,
        explanation: "Toán tử 'not' đảo ngược giá trị Boolean, nên not False = True."
    },
    {
        question: "Kết quả của 5 > 3 and 10 < 20 là gì?",
        options: ["True", "False", "None", "Lỗi"],
        correct: 0,
        explanation: "5 > 3 là True và 10 < 20 là True, nên True and True = True."
    },
    {
        question: "Kết quả của 5 > 10 or 10 < 20 là gì?",
        options: ["True", "False", "None", "Lỗi"],
        correct: 0,
        explanation: "5 > 10 là False nhưng 10 < 20 là True, nên False or True = True."
    },
    {
        question: "Kết quả của not (5 > 3) là gì?",
        options: ["True", "False", "None", "Lỗi"],
        correct: 1,
        explanation: "5 > 3 là True, nên not (True) = False."
    },
    {
        question: "Cho x = 10. Kết quả của x > 5 and x < 15 là gì?",
        options: ["True", "False", "10", "x"],
        correct: 0,
        explanation: "x = 10, nên 10 > 5 là True và 10 < 15 là True, nên True and True = True."
    },
    {
        question: "Cho age = 18. Kết quả của age >= 18 là gì?",
        options: ["True", "False", "18", "None"],
        correct: 0,
        explanation: "age = 18, nên 18 >= 18 là True (18 lớn hơn hoặc bằng 18)."
    },
    {
        question: "Kết quả của False and True là gì?",
        options: ["True", "False", "None", "Lỗi"],
        correct: 1,
        explanation: "Toán tử 'and' chỉ trả về True khi cả hai vế đều True. False and True = False."
    },
    {
        question: "Kết quả của True or True là gì?",
        options: ["True", "False", "2", "Lỗi"],
        correct: 0,
        explanation: "Toán tử 'or' trả về True khi ít nhất một vế là True. True or True = True."
    },
    {
        question: "Biểu thức 10 <= x <= 20 trong Python dùng để kiểm tra điều gì?",
        options: ["x bằng 10 hoặc 20", "x nhỏ hơn 10 hoặc lớn hơn 20", "x lớn hơn hoặc bằng 10 và nhỏ hơn hoặc bằng 20", "Gây lỗi cú pháp"],
        correct: 2,
        explanation: "Biểu thức 10 <= x <= 20 kiểm tra x có nằm trong khoảng từ 10 đến 20 (bao gồm cả 10 và 20) hay không."
    },
    {
        question: "Kết quả của (10 + 5) == 15 là gì?",
        options: ["True", "False", "15", "Lỗi"],
        correct: 0,
        explanation: "(10 + 5) = 15, và 15 == 15 là True."
    },
    {
        question: "Kết quả của \"abc\" < \"abd\" là gì? (Python so sánh chuỗi theo thứ tự từ điển)",
        options: ["True", "False", "Lỗi", "None"],
        correct: 0,
        explanation: "Python so sánh chuỗi theo thứ tự từ điển (lexicographic). \"abc\" đứng trước \"abd\" nên \"abc\" < \"abd\" là True."
    },
    {
        question: "Kết quả của \"Zebra\" < \"Apple\" là gì?",
        options: ["True", "False", "Lỗi", "None"],
        correct: 1,
        explanation: "Theo thứ tự từ điển, \"Zebra\" đứng sau \"Apple\" (Z đứng sau A trong bảng chữ cái), nên \"Zebra\" < \"Apple\" là False."
    },
    {
        question: "Cho has_ticket = True, is_vip = False. Kết quả của has_ticket or is_vip là gì?",
        options: ["True", "False", "None", "Lỗi"],
        correct: 0,
        explanation: "has_ticket = True, is_vip = False. True or False = True."
    },
    {
        question: "Toán tử and sẽ trả về True khi nào?",
        options: ["Khi ít nhất một vế là True", "Khi cả hai vế là True", "Khi ít nhất một vế là False", "Khi cả hai vế là False"],
        correct: 1,
        explanation: "Toán tử 'and' chỉ trả về True khi cả hai vế đều là True."
    },
    {
        question: "Toán tử or sẽ trả về False khi nào?",
        options: ["Khi ít nhất một vế là True", "Khi cả hai vế là True", "Khi ít nhất một vế là False", "Khi cả hai vế là False"],
        correct: 3,
        explanation: "Toán tử 'or' chỉ trả về False khi cả hai vế đều là False."
    },
    // MỨC ĐỘ KHÓ (Vận dụng cao & Phân tích, Đánh giá)
    {
        question: "Kết quả của True or False and False là gì?",
        options: ["True", "False", "Lỗi", "None"],
        correct: 0,
        explanation: "Vì 'and' có độ ưu tiên cao hơn 'or', biểu thức được tính như: True or (False and False) = True or False = True."
    },
    {
        question: "Kết quả của (True or False) and False là gì?",
        options: ["True", "False", "Lỗi", "None"],
        correct: 1,
        explanation: "(True or False) = True, sau đó True and False = False."
    },
    {
        question: "Độ ưu tiên của các toán tử logic nào sau đây là đúng (từ cao đến thấp)?",
        options: ["and -> or -> not", "or -> and -> not", "not -> and -> or", "not -> or -> and"],
        correct: 2,
        explanation: "Thứ tự ưu tiên trong Python: not (cao nhất) -> and -> or (thấp nhất)."
    },
    {
        question: "Kết quả của not True or False là gì?",
        options: ["True", "False", "None", "Lỗi"],
        correct: 1,
        explanation: "Vì 'not' có ưu tiên cao nhất: (not True) or False = False or False = False."
    },
    {
        question: "Kết quả của not (True or False) là gì?",
        options: ["True", "False", "None", "Lỗi"],
        correct: 1,
        explanation: "(True or False) = True, sau đó not True = False."
    },
    {
        question: "Cho x = 5. Kết quả của x > 0 and x < 10 and x % 2 == 0 là gì?",
        options: ["True", "False", "5", "Lỗi"],
        correct: 1,
        explanation: "x = 5: x > 0 là True, x < 10 là True, nhưng x % 2 == 0 là 5 % 2 == 0 tức 1 == 0 là False. Do đó cả biểu thức and là False."
    },
    {
        question: "Cho score = 80, attendance = 95. Biểu thức score >= 75 and attendance >= 90 trả về kết quả gì?",
        options: ["True", "False", "80", "95"],
        correct: 0,
        explanation: "score >= 75 là 80 >= 75 = True, attendance >= 90 là 95 >= 90 = True. True and True = True."
    },
    {
        question: "Kết quả của 5 > 10 and 10 / 0 là gì?",
        options: ["Lỗi chia cho không (ZeroDivisionError)", "True", "False", "None"],
        correct: 2,
        explanation: "Vì vế đầu 5 > 10 là False, Python sẽ không cần xét vế sau của toán tử and (short-circuit evaluation), nên không có lỗi chia cho 0."
    },
    {
        question: "Kết quả của 10 > 5 or 10 / 0 là gì?",
        options: ["Lỗi chia cho không (ZeroDivisionError)", "True", "False", "None"],
        correct: 1,
        explanation: "Vì vế đầu 10 > 5 là True, Python sẽ trả về True ngay lập tức mà không cần xét vế sau của toán tử or (short-circuit evaluation)."
    },
    {
        question: "Biểu thức bool(0) trả về giá trị gì?",
        options: ["True", "False", "0", "Lỗi"],
        correct: 1,
        explanation: "Số 0 được coi là giá trị falsy trong Python, nên bool(0) = False."
    },
    {
        question: "Biểu thức bool(\"hello\") trả về giá trị gì?",
        options: ["True", "False", "\"hello\"", "Lỗi"],
        correct: 0,
        explanation: "Chuỗi không rỗng được coi là giá trị truthy trong Python, nên bool(\"hello\") = True."
    },
    {
        question: "Biểu thức bool(\"\") (chuỗi rỗng) trả về giá trị gì?",
        options: ["True", "False", "\"\"", "Lỗi"],
        correct: 1,
        explanation: "Chuỗi rỗng được coi là giá trị falsy trong Python, nên bool(\"\") = False."
    },
    {
        question: "Kết quả của not (5 != 5 or 3 < 4) and 10 > 5 là gì?",
        options: ["True", "False", "None", "Lỗi"],
        correct: 1,
        explanation: "Bên trong ngoặc: 5 != 5 là False, 3 < 4 là True, nên False or True = True. not True = False. Cả biểu thức: False and True = False."
    },
    {
        question: "Cho x = True, y = False, z = True. Kết quả của (x or y) and not z là gì?",
        options: ["True", "False", "None", "Lỗi"],
        correct: 1,
        explanation: "(x or y) = (True or False) = True. not z = not True = False. Cả biểu thức: True and False = False."
    },
    {
        question: "Trong Python, biểu thức 0 < x < 10 tương đương với biểu thức logic nào sau đây?",
        options: ["x > 0 or x < 10", "x > 0 and x < 10", "not (x <= 0 or x >= 10)", "Cả B và C đều đúng"],
        correct: 3,
        explanation: "0 < x < 10 tương đương với x > 0 and x < 10. Theo định luật De Morgan, điều này cũng tương đương với not (x <= 0 or x >= 10)."
    }
];

// Support both CommonJS and ES6 modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = quizData;
}

export default quizData;
