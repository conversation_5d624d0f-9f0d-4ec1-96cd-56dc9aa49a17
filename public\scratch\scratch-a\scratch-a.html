<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scratch - A | Classroom Web</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../../../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../../../assets/images/favicon.png">

    <link rel="stylesheet" href="../../../assets/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Dark Background with Starfield Effect */
        body {
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Starfield Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(2px 2px at 20px 30px, #fff, transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
                radial-gradient(1px 1px at 90px 40px, #fff, transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
                radial-gradient(2px 2px at 160px 30px, #fff, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: sparkle 3s linear infinite;
            opacity: 0.3;
            z-index: -1;
        }

        @keyframes sparkle {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-100px); }
        }

        .class-header {
            background: transparent;
            color: white;
            padding: 60px 0;
            text-align: center;
            margin-top: 80px;
            position: relative;
            z-index: 1;
        }

        .class-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            word-wrap: break-word;
        }

        .class-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            word-wrap: break-word;
        }

        @media (max-width: 768px) {
            .class-header h1 {
                font-size: 2rem;
            }
            .class-header p {
                font-size: 1rem;
            }
        }

        .class-info {
            background: transparent;
            padding: 40px 0;
            position: relative;
            z-index: 1;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .info-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .info-card:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
        }

        .info-card i {
            font-size: 2.5rem;
            color: #FFD700;
            margin-bottom: 15px;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        }

        .info-card h3 {
            color: white;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .info-card p {
            color: rgba(255, 255, 255, 0.9);
        }

        .access-denied {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: #ff6b6b;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }

        .access-granted {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4caf50;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }

        .lessons-section {
            background: transparent;
            padding: 40px 0;
            position: relative;
            z-index: 1;
        }

        .lessons-navigation {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .lesson-tab {
            background: white;
            border: 2px solid #FF8C00;
            color: #FF8C00;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            min-width: 120px;
            text-align: center;
        }

        .lesson-tab:hover {
            background: #FF8C00;
            color: white;
            transform: translateY(-2px);
        }

        .lesson-tab.active {
            background: #FF8C00;
            color: white;
            box-shadow: 0 4px 15px rgba(255, 140, 0, 0.3);
        }

        .lesson-content {
            display: none;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            animation: fadeIn 0.3s ease-in-out;
        }

        .lesson-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .lesson-content h3 {
            color: #FF8C00;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .lesson-objectives {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }

        .lesson-objectives h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .lesson-objectives ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .lesson-objectives li {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .lesson-details {
            margin: 20px 0;
        }

        .lesson-details h4 {
            color: #FF8C00;
            margin: 15px 0 10px 0;
        }

        .lesson-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .status-info {
            flex: 1;
        }

        .assignment-button {
            margin-left: 20px;
        }

        .meet-link {
            display: inline-block;
            background: linear-gradient(135deg, #FFD700, #FF8C00);
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            margin-top: 15px;
            transition: transform 0.3s ease;
        }

        .meet-link:hover {
            transform: translateY(-2px);
        }

        .students-display {
            margin-top: 20px;
        }

        .student-avatars {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .student-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #FFD700, #FF8C00);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
            position: relative;
            cursor: pointer;
        }

        .student-avatar:hover::after {
            content: attr(data-name);
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
        }

        .student-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .coming-soon {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin: 40px 0;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .coming-soon h3 {
            color: #FFD700;
            margin-bottom: 15px;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        }

        .coming-soon i {
            font-size: 3rem;
            color: #FFD700;
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        }

        .coming-soon p {
            color: rgba(255, 255, 255, 0.9);
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../../index.html">Trang Chủ</a></li>
                    <li><a href="../../index.html">Lớp Học</a></li>
                    <li><a href="../../../achievements/">Thành Tích</a></li>
                    <li><a href="../../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../../research/">Sự kiện</a></li>
                    <li><a href="../../../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Class Header -->
    <section class="class-header">
        <div class="container">
            <h1><i class="fas fa-puzzle-piece"></i> Scratch - A</h1>
            <p>Lớp học Scratch và Tin học cơ bản - T2 - T4 19h30 đến 21h00</p>
        </div>
    </section>

    <!-- Access Control Message -->
    <section class="class-info">
        <div class="container">
            <div id="accessMessage" style="display: none;"></div>
            
            <div id="classContent" style="display: none;">
                <!-- Class Information -->
                <div class="info-grid">
                    <div class="info-card">
                        <i class="fas fa-calendar-alt"></i>
                        <h3>Lịch Học</h3>
                        <p>Thứ 2 - Thứ 4<br>19:30 - 21:00</p>
                    </div>
                    <div class="info-card">
                        <i class="fas fa-users"></i>
                        <h3>Học Viên</h3>
                        <p id="studentCount">Đang tải...</p>
                        <div class="students-display">
                            <div id="studentAvatars" class="student-avatars"></div>
                        </div>
                    </div>
                    <div class="info-card">
                        <i class="fas fa-video"></i>
                        <h3>Google Meet</h3>
                        <a href="https://meet.google.com/xot-zprw-ote" target="_blank" class="meet-link">
                            <i class="fas fa-video"></i> Tham gia lớp học
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Lessons Section -->
    <section class="lessons-section" id="lessonsSection" style="display: none;">
        <div class="container">
            <h2 style="color: white; text-align: center; margin-bottom: 30px;">Nội Dung Bài Học</h2>

            <!-- Lesson Navigation -->
            <div class="lessons-navigation">
                <div class="lesson-tab active" onclick="showLesson(1)">
                    <i class="fas fa-play-circle"></i> Bài 1
                </div>
                <div class="lesson-tab" onclick="showLesson(2)">
                    <i class="fas fa-desktop"></i> Bài 2
                </div>
                <div class="lesson-tab" onclick="showLesson(3)">
                    <i class="fas fa-user"></i> Bài 3
                </div>
                <div class="lesson-tab" onclick="showLesson(4)">
                    <i class="fas fa-volume-up"></i> Bài 4
                </div>
                <div class="lesson-tab" onclick="showLesson(5)">
                    <i class="fas fa-sync-alt"></i> Bài 5
                </div>
                <div class="lesson-tab" onclick="showLesson(6)">
                    <i class="fas fa-brain"></i> Bài 6
                </div>
                <div class="lesson-tab" onclick="showLesson(7)">
                    <i class="fas fa-code-branch"></i> Bài 7
                </div>
            </div>

            <!-- Lesson 1 Content -->
            <div id="lesson1" class="lesson-content active">
                <h3><i class="fas fa-play-circle"></i> Bài 1: Chào Bạn, Nhà Lập Trình Nhí!</h3>

                <div class="lesson-objectives">
                    <h4>Mục tiêu:</h4>
                    <ul>
                        <li>Hiểu lập trình là gì, biết Scratch là gì và tạo tài khoản thành công.</li>
                    </ul>
                </div>

                <div class="lesson-details">
                    <h4><i class="fas fa-book"></i> Nội dung chính:</h4>
                    <div class="lesson-objectives">
                        <p><strong>1. Giới thiệu về lập trình:</strong></p>
                        <ul>
                            <li>Lập trình là gì? (Cách con người "nói chuyện" và "ra lệnh" cho máy tính).</li>
                            <li>Tại sao nên học lập trình? (Phát triển tư duy logic, sáng tạo, giải quyết vấn đề).</li>
                        </ul>

                        <p><strong>2. Giới thiệu về Scratch:</strong></p>
                        <ul>
                            <li>Scratch là gì? Ngôn ngữ lập trình trực quan dành cho trẻ em.</li>
                            <li>Khám phá các sản phẩm mẫu trên trang web Scratch.</li>
                            <li>Hướng dẫn tạo tài khoản Scratch.</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-tasks"></i> Bài tập thực hành (Thử thách khám phá):</h4>
                    <div class="lesson-objectives">
                        <ol>
                            <li><strong>Đăng nhập thành công:</strong> Tự mình đăng nhập vào tài khoản Scratch đã tạo.</li>
                            <li><strong>Tìm kiếm dự án:</strong> Dùng thanh tìm kiếm, gõ một từ khóa con thích (VD: "mèo", "ô tô", "công chúa") và xem một dự án bất kỳ.</li>
                            <li><strong>Khám phá bên trong:</strong> Bấm vào nút "See inside" (Xem bên trong) của một dự án để xem các khối lệnh người khác đã làm.</li>
                            <li><strong>"Thả tim" và "Theo dõi":</strong> Tìm một dự án con thích, bấm vào hình trái tim (Love) và ngôi sao (Favorite). Tìm tác giả và bấm nút "Follow".</li>
                            <li><strong>Tùy chỉnh hồ sơ:</strong> Nhờ bố mẹ giúp thay đổi ảnh đại diện (avatar) của mình bằng một hình ảnh yêu thích.</li>
                        </ol>
                    </div>

                    <p><strong><i class="fas fa-clock"></i> Thời lượng dự kiến:</strong> 1 buổi</p>
                    <p><strong><i class="fas fa-link"></i> Tài liệu:</strong>
                        <a href="https://scratch.mit.edu/" target="_blank" style="color: #FF8C00;">Truy cập Scratch</a>
                    </p>
                </div>

                <div class="lesson-status">
                    <div class="status-info">
                        <p><strong><i class="fas fa-info-circle"></i> Trạng thái:</strong>
                            <span id="lesson1Status">Đang tải...</span>
                        </p>
                    </div>
                    <div class="assignment-button">
                        <a href="https://scratch.mit.edu/" target="_blank" class="meet-link" id="lesson1Button">
                            <i class="fas fa-external-link-alt"></i> Tiến hành làm
                        </a>
                    </div>
                </div>
            </div>

            <!-- Lesson 2 Content -->
            <div id="lesson2" class="lesson-content">
                <h3><i class="fas fa-desktop"></i> Bài 2: Khám Phá Sân Khấu Scratch</h3>

                <div class="lesson-objectives">
                    <h4>Mục tiêu:</h4>
                    <ul>
                        <li>Làm quen giao diện, hiểu khái niệm nhân vật/sân khấu, chạy được chương trình đầu tiên.</li>
                    </ul>
                </div>

                <div class="lesson-details">
                    <h4><i class="fas fa-book"></i> Nội dung chính:</h4>
                    <div class="lesson-objectives">
                        <p><strong>1. Khám phá giao diện Scratch:</strong></p>
                        <ul>
                            <li>Sân khấu (Stage) và nhân vật (Sprite).</li>
                            <li>Khu vực khối lệnh (Blocks Palette).</li>
                            <li>Khu vực viết mã (Scripts Area).</li>
                        </ul>

                        <p><strong>2. Giới thiệu cờ xanh/nút đỏ:</strong></p>
                        <ul>
                            <li>Cờ xanh để bắt đầu chương trình.</li>
                            <li>Nút đỏ để dừng chương trình.</li>
                        </ul>

                        <p><strong>3. Thực hành kéo-thả lệnh:</strong></p>
                        <ul>
                            <li>Cách kéo khối lệnh từ palette vào scripts area.</li>
                            <li>Cách ghép các khối lệnh với nhau.</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-tasks"></i> Bài tập thực hành (Làm quen công cụ):</h4>
                    <div class="lesson-objectives">
                        <ol>
                            <li><strong>Lời chào đầu tiên:</strong> Kéo khối lệnh "say Hello! for 2 seconds" và bấm vào nó để chú mèo nói chuyện. Đổi chữ "Hello!" thành tên của con.</li>
                            <li><strong>Bước đi đầu:</strong> Kéo khối lệnh "move 10 steps" và bấm vào nó nhiều lần để xem chú mèo di chuyển.</li>
                            <li><strong>Xoay vòng:</strong> Kéo khối lệnh "turn 15 degrees" và bấm vào nó để xem chú mèo xoay.</li>
                            <li><strong>Kết hợp đơn giản:</strong> Ghép 2 khối lệnh "move 100 steps" và "say Mình đến nơi rồi!". Chạy thử bằng cách bấm vào khối lệnh trên cùng.</li>
                            <li><strong>Chương trình hoàn chỉnh đầu tiên:</strong> Ghép khối "when green flag clicked" lên trên cùng của chuỗi lệnh ở bài 4. Bây giờ hãy thử bấm vào lá cờ xanh để chạy chương trình.</li>
                        </ol>
                    </div>

                    <p><strong><i class="fas fa-clock"></i> Thời lượng dự kiến:</strong> 1 buổi</p>
                    <p><strong><i class="fas fa-link"></i> Tài liệu:</strong>
                        <a href="https://scratch.mit.edu/" target="_blank" style="color: #FF8C00;">Truy cập Scratch</a>
                    </p>
                </div>

                <div class="lesson-status">
                    <div class="status-info">
                        <p><strong><i class="fas fa-info-circle"></i> Trạng thái:</strong>
                            <span id="lesson2Status">Đang tải...</span>
                        </p>
                    </div>
                    <div class="assignment-button">
                        <a href="https://scratch.mit.edu/" target="_blank" class="meet-link" id="lesson2Button">
                            <i class="fas fa-external-link-alt"></i> Tiến hành làm
                        </a>
                    </div>
                </div>
            </div>

            <!-- Lesson 3 Content -->
            <div id="lesson3" class="lesson-content">
                <h3><i class="fas fa-user"></i> Bài 3: Nhân Vật Đầu Tiên Của Em</h3>

                <div class="lesson-objectives">
                    <h4>Mục tiêu:</h4>
                    <ul>
                        <li>Biết cách quản lý nhân vật, phông nền và sử dụng các khối lệnh cơ bản về chuyển động, hiển thị.</li>
                    </ul>
                </div>

                <div class="lesson-details">
                    <h4><i class="fas fa-book"></i> Nội dung chính:</h4>
                    <div class="lesson-objectives">
                        <p><strong>1. Thư viện nhân vật/phông nền:</strong></p>
                        <ul>
                            <li>Cách chọn nhân vật mới từ thư viện.</li>
                            <li>Cách thay đổi phông nền sân khấu.</li>
                        </ul>

                        <p><strong>2. Các thuộc tính nhân vật:</strong></p>
                        <ul>
                            <li>Kích thước (size) và hướng (direction).</li>
                            <li>Tọa độ (x, y) trên sân khấu.</li>
                        </ul>

                        <p><strong>3. Các khối lệnh cơ bản:</strong></p>
                        <ul>
                            <li>Chuyển động: move, turn, go to x: y:</li>
                            <li>Hiển thị: say, think, change size by, show, hide</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-tasks"></i> Bài tập thực hành (Sáng tạo cảnh đầu tiên):</h4>
                    <div class="lesson-objectives">
                        <ol>
                            <li><strong>Sân khấu trong mơ:</strong> Chọn một phông nền con thích (VD: bãi biển, vũ trụ) và một nhân vật khác thay cho chú mèo.</li>
                            <li><strong>Phi hành gia đi dạo:</strong> Chọn phông nền mặt trăng và nhân vật phi hành gia. Lập trình để khi bấm cờ xanh, phi hành gia di chuyển từ trái qua phải sân khấu.</li>
                            <li><strong>Khủng long biến hình:</strong> Chọn nhân vật khủng long. Lập trình để khi bấm cờ xanh, khủng long nói "Gràooo!", sau đó to lên (change size by 50), rồi nhỏ lại (change size by -50).</li>
                            <li><strong>Trốn tìm:</strong> Lập trình cho nhân vật: Bắt đầu thì show, đợi 2 giây rồi hide, đợi 2 giây nữa rồi lại show.</li>
                            <li><strong>Dịch chuyển tức thời:</strong> Chọn phông nền sân khấu. Thêm 2 nhân vật ca sĩ. Dùng khối "go to x: y:" để lập trình cho ca sĩ 1 đứng ở bên trái sân khấu và ca sĩ 2 đứng ở bên phải.</li>
                        </ol>
                    </div>

                    <p><strong><i class="fas fa-clock"></i> Thời lượng dự kiến:</strong> 1 buổi</p>
                    <p><strong><i class="fas fa-link"></i> Tài liệu:</strong>
                        <a href="https://scratch.mit.edu/" target="_blank" style="color: #FF8C00;">Truy cập Scratch</a>
                    </p>
                </div>

                <div class="lesson-status">
                    <div class="status-info">
                        <p><strong><i class="fas fa-info-circle"></i> Trạng thái:</strong>
                            <span id="lesson3Status">Đang tải...</span>
                        </p>
                    </div>
                    <div class="assignment-button">
                        <a href="https://scratch.mit.edu/" target="_blank" class="meet-link" id="lesson3Button">
                            <i class="fas fa-external-link-alt"></i> Tiến hành làm
                        </a>
                    </div>
                </div>
            </div>

            <!-- Lesson 4 Content -->
            <div id="lesson4" class="lesson-content">
                <h3><i class="fas fa-volume-up"></i> Bài 4: Sự Kiện và Âm Thanh</h3>

                <div class="lesson-objectives">
                    <h4>Mục tiêu:</h4>
                    <ul>
                        <li>Hiểu vai trò của nhóm Sự kiện, biết cách bắt các sự kiện khác nhau và thêm âm thanh vào chương trình.</li>
                    </ul>
                </div>

                <div class="lesson-details">
                    <h4><i class="fas fa-book"></i> Nội dung chính:</h4>
                    <div class="lesson-objectives">
                        <p><strong>1. Nhóm Sự kiện (Events):</strong></p>
                        <ul>
                            <li>Khối "when green flag clicked" - Bắt đầu chương trình.</li>
                            <li>Khối "when this sprite clicked" - Khi nhấp vào nhân vật.</li>
                            <li>Khối "when key pressed" - Khi nhấn phím.</li>
                        </ul>

                        <p><strong>2. Nhóm Âm thanh (Sound):</strong></p>
                        <ul>
                            <li>Khối "play sound" - Phát âm thanh.</li>
                            <li>Khối "start sound" - Bắt đầu phát âm thanh.</li>
                            <li>Cách thêm âm thanh từ thư viện.</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-tasks"></i> Bài tập thực hành (Làm chủ tương tác):</h4>
                    <div class="lesson-objectives">
                        <ol>
                            <li><strong>Nút bấm biết kêu:</strong> Chọn một nhân vật (VD: cái chuông). Lập trình để when this sprite clicked, nó sẽ phát ra âm thanh "Boing".</li>
                            <li><strong>Chào theo phím bấm:</strong> Lập trình để when space key pressed, nhân vật sẽ nói "Xin chào các bạn!".</li>
                            <li><strong>Dàn nhạc động vật:</strong> Chọn 3 nhân vật động vật (chó, mèo, gà). Lập trình để khi bấm vào mỗi con vật, nó sẽ kêu tiếng của con vật đó.</li>
                            <li><strong>Ô tô khởi động:</strong> Chọn nhân vật ô tô. Lập trình để when green flag clicked, nó phát ra tiếng động cơ, sau đó di chuyển một đoạn ngắn.</li>
                            <li><strong>Bàn phím ma thuật:</strong> Lập trình cho 1 nhân vật. when up arrow key pressed, nhân vật di chuyển lên trên. when down arrow key pressed, nhân vật di chuyển xuống dưới.</li>
                        </ol>
                    </div>

                    <p><strong><i class="fas fa-clock"></i> Thời lượng dự kiến:</strong> 1 buổi</p>
                    <p><strong><i class="fas fa-link"></i> Tài liệu:</strong>
                        <a href="https://scratch.mit.edu/" target="_blank" style="color: #FF8C00;">Truy cập Scratch</a>
                    </p>
                </div>

                <div class="lesson-status">
                    <div class="status-info">
                        <p><strong><i class="fas fa-info-circle"></i> Trạng thái:</strong>
                            <span id="lesson4Status">Đang tải...</span>
                        </p>
                    </div>
                    <div class="assignment-button">
                        <a href="https://scratch.mit.edu/" target="_blank" class="meet-link" id="lesson4Button">
                            <i class="fas fa-external-link-alt"></i> Tiến hành làm
                        </a>
                    </div>
                </div>
            </div>

            <!-- Lesson 5 Content -->
            <div id="lesson5" class="lesson-content">
                <h3><i class="fas fa-sync-alt"></i> Bài 5: Vòng Lặp Diệu Kỳ</h3>

                <div class="lesson-objectives">
                    <h4>Mục tiêu:</h4>
                    <ul>
                        <li>Hiểu sự cần thiết của vòng lặp, sử dụng thành thạo khối repeat và forever.</li>
                    </ul>
                </div>

                <div class="lesson-details">
                    <h4><i class="fas fa-book"></i> Nội dung chính:</h4>
                    <div class="lesson-objectives">
                        <p><strong>1. Khái niệm vòng lặp:</strong></p>
                        <ul>
                            <li>Tại sao cần vòng lặp? (Tránh lặp lại code nhiều lần).</li>
                            <li>Khối "repeat" - Lặp lại một số lần nhất định.</li>
                            <li>Khối "forever" - Lặp lại mãi mãi.</li>
                        </ul>

                        <p><strong>2. Ứng dụng vòng lặp:</strong></p>
                        <ul>
                            <li>Tạo hoạt ảnh liên tục.</li>
                            <li>Vẽ hình học với Pen.</li>
                            <li>Tạo hiệu ứng đặc biệt.</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-tasks"></i> Bài tập thực hành (Sức mạnh của sự lặp lại):</h4>
                    <div class="lesson-objectives">
                        <ol>
                            <li><strong>Vỗ tay 10 lần:</strong> Lập trình cho nhân vật thay đổi giữa 2 trang phục liên tục 10 lần để tạo hiệu ứng vỗ tay. Sử dụng khối repeat 10, bên trong là next costume và wait 0.3 seconds.</li>
                            <li><strong>Vẽ hình vuông:</strong> Dùng khối Pen (Phần mở rộng). Lập trình dùng vòng lặp repeat 4, bên trong là khối move 100 steps và turn 90 degrees.</li>
                            <li><strong>Cánh quạt quay mãi:</strong> Chọn một nhân vật hình cánh quạt (hoặc tự vẽ). Dùng vòng lặp forever, bên trong là khối turn 15 degrees.</li>
                            <li><strong>Ngôi sao lấp lánh:</strong> Dùng vòng lặp forever kết hợp các khối change color effect by 25 và wait 0.5 seconds để tạo hiệu ứng đổi màu liên tục.</li>
                            <li><strong>Điệu nhảy không ngừng:</strong> Dùng forever để nhân vật liên tục move 10 steps và if on edge, bounce, tạo hoạt ảnh di chuyển qua lại không ngừng.</li>
                        </ol>
                    </div>

                    <p><strong><i class="fas fa-clock"></i> Thời lượng dự kiến:</strong> 1 buổi</p>
                    <p><strong><i class="fas fa-link"></i> Tài liệu:</strong>
                        <a href="https://scratch.mit.edu/" target="_blank" style="color: #FF8C00;">Truy cập Scratch</a>
                    </p>
                </div>

                <div class="lesson-status">
                    <div class="status-info">
                        <p><strong><i class="fas fa-info-circle"></i> Trạng thái:</strong>
                            <span id="lesson5Status">Đang tải...</span>
                        </p>
                    </div>
                    <div class="assignment-button">
                        <a href="https://scratch.mit.edu/" target="_blank" class="meet-link" id="lesson5Button">
                            <i class="fas fa-external-link-alt"></i> Tiến hành làm
                        </a>
                    </div>
                </div>
            </div>

            <!-- Lesson 6 Content -->
            <div id="lesson6" class="lesson-content">
                <h3><i class="fas fa-brain"></i> Bài 6: Khi Nhân Vật Biết "Suy Nghĩ" – Điều Kiện Nếu... Thì...</h3>

                <div class="lesson-objectives">
                    <h4>Mục tiêu:</h4>
                    <ul>
                        <li>Hiểu khái niệm điều kiện, sử dụng khối if ... then ... và các khối Cảm biến.</li>
                    </ul>
                </div>

                <div class="lesson-details">
                    <h4><i class="fas fa-book"></i> Nội dung chính:</h4>
                    <div class="lesson-objectives">
                        <p><strong>1. Cấu trúc điều kiện:</strong></p>
                        <ul>
                            <li>Khái niệm "nếu... thì..." trong lập trình.</li>
                            <li>Khối "if ... then ..." - Thực hiện lệnh khi điều kiện đúng.</li>
                            <li>Cách sử dụng các khối điều kiện.</li>
                        </ul>

                        <p><strong>2. Các khối Cảm biến (Sensing):</strong></p>
                        <ul>
                            <li>Khối "touching ...?" - Kiểm tra va chạm.</li>
                            <li>Khối "key ... pressed?" - Kiểm tra phím được nhấn.</li>
                            <li>Khối "touching color ...?" - Kiểm tra chạm màu.</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-tasks"></i> Bài tập thực hành (Nhân vật thông minh):</h4>
                    <div class="lesson-objectives">
                        <ol>
                            <li><strong>Chạy trốn con trỏ:</strong> Dùng forever, bên trong có khối if touching mouse-pointer? then say "Bắt được mình rồi!".</li>
                            <li><strong>Tự động quay đầu:</strong> Lập trình cho nhân vật di chuyển liên tục. Dùng khối if on edge, bounce để nó tự động quay lại khi chạm cạnh.</li>
                            <li><strong>Bấm phím để nhảy:</strong> Dùng forever, bên trong có khối if key space pressed? then (cho nhân vật đi lên 50 bước, rồi đi xuống 50 bước để tạo hiệu ứng nhảy).</li>
                            <li><strong>Ăn bánh:</strong> Tạo một nhân vật người và một nhân vật bánh. Lập trình cho người di chuyển theo chuột. if touching &lt;nhân vật bánh&gt;? then cho cái bánh biến mất (hide).</li>
                            <li><strong>Mê cung đơn giản:</strong> Vẽ một mê cung đơn giản làm phông nền (dùng màu đen cho tường). Lập trình cho nhân vật di chuyển theo chuột. if touching color &lt;màu đen&gt;? then cho nhân vật quay về vị trí xuất phát.</li>
                        </ol>
                    </div>

                    <p><strong><i class="fas fa-clock"></i> Thời lượng dự kiến:</strong> 1 buổi</p>
                    <p><strong><i class="fas fa-link"></i> Tài liệu:</strong>
                        <a href="https://scratch.mit.edu/" target="_blank" style="color: #FF8C00;">Truy cập Scratch</a>
                    </p>
                </div>

                <div class="lesson-status">
                    <div class="status-info">
                        <p><strong><i class="fas fa-info-circle"></i> Trạng thái:</strong>
                            <span id="lesson6Status">Đang tải...</span>
                        </p>
                    </div>
                    <div class="assignment-button">
                        <a href="https://scratch.mit.edu/" target="_blank" class="meet-link" id="lesson6Button">
                            <i class="fas fa-external-link-alt"></i> Tiến hành làm
                        </a>
                    </div>
                </div>
            </div>

            <!-- Lesson 7 Content -->
            <div id="lesson7" class="lesson-content">
                <h3><i class="fas fa-code-branch"></i> Bài 7: Nhiều Lựa Chọn Hơn – Nếu... Thì... Nếu Không Thì...</h3>

                <div class="lesson-objectives">
                    <h4>Mục tiêu:</h4>
                    <ul>
                        <li>Hiểu và sử dụng khối if ... then ... else ... để xử lý các tình huống có hai lựa chọn.</li>
                    </ul>
                </div>

                <div class="lesson-details">
                    <h4><i class="fas fa-book"></i> Nội dung chính:</h4>
                    <div class="lesson-objectives">
                        <p><strong>1. Cấu trúc if...then...else:</strong></p>
                        <ul>
                            <li>So sánh if...then và if...then...else.</li>
                            <li>Khi nào sử dụng if...then...else.</li>
                            <li>Cách kết hợp với khối ask...and wait.</li>
                        </ul>

                        <p><strong>2. Tương tác với người dùng:</strong></p>
                        <ul>
                            <li>Khối "ask ... and wait" - Hỏi người dùng.</li>
                            <li>Khối "answer" - Lấy câu trả lời.</li>
                            <li>So sánh câu trả lời với giá trị mong muốn.</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-tasks"></i> Bài tập thực hành (Ra quyết định):</h4>
                    <div class="lesson-objectives">
                        <ol>
                            <li><strong>Trả lời câu hỏi:</strong> Dùng khối ask "Thủ đô của Việt Nam là gì?" and wait. Dùng if answer = "Hà Nội" then say "Đúng rồi!" else say "Sai rồi!".</li>
                            <li><strong>Phân chia sân khấu:</strong> Lập trình nhân vật. Dùng forever, bên trong có if mouse x > 0 then (nhân vật nói "Bạn đang ở bên phải!") else (nhân vật nói "Bạn đang ở bên trái!").</li>
                            <li><strong>Công tắc đèn:</strong> Tạo nhân vật bóng đèn có 2 trang phục ("sáng" và "tối"). when this sprite clicked, if costume name = "tối" then chuyển sang trang phục "sáng" else chuyển về "tối".</li>
                            <li><strong>Cá mập săn mồi:</strong> Tạo 1 cá mập (người chơi) và 1 cá nhỏ. if cá mập chạm cá nhỏ then cá mập nói "Ngon quá!" else cá mập nói "Đói bụng quá!".</li>
                            <li><strong>Game đoán số:</strong> Cho máy tính chọn một số ngẫu nhiên (1 hoặc 2) và lưu vào một biến. Yêu cầu người chơi đoán. Nếu đoán đúng (answer = biến), nhân vật nói "Bạn thắng!", nếu không thì nói "Bạn thua rồi!".</li>
                        </ol>
                    </div>

                    <p><strong><i class="fas fa-clock"></i> Thời lượng dự kiến:</strong> 1 buổi</p>
                    <p><strong><i class="fas fa-link"></i> Tài liệu:</strong>
                        <a href="https://scratch.mit.edu/" target="_blank" style="color: #FF8C00;">Truy cập Scratch</a>
                    </p>
                </div>

                <div class="lesson-status">
                    <div class="status-info">
                        <p><strong><i class="fas fa-info-circle"></i> Trạng thái:</strong>
                            <span id="lesson7Status">Đang tải...</span>
                        </p>
                    </div>
                    <div class="assignment-button">
                        <a href="https://scratch.mit.edu/" target="_blank" class="meet-link" id="lesson7Button">
                            <i class="fas fa-external-link-alt"></i> Tiến hành làm
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, collection, query, where, getDocs } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Class ID for this class
        const CLASS_ID = 'scratch-a';

        // Check if user has access to this class
        async function checkAccess(user) {
            if (!user) {
                return false;
            }

            try {
                const userDoc = await getDoc(doc(db, "users", user.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();

                    // Check if user is admin
                    const isAdmin = userData.isAdmin || user.email === '<EMAIL>';

                    // Admin has access to all classes, or user has access to their selected class
                    return isAdmin ||
                        userData.courseClass === CLASS_ID || // backward compatibility
                        (userData.allowedClasses && userData.allowedClasses.includes(CLASS_ID));
                }
                return false;
            } catch (error) {
                console.error("Error checking access:", error);
                return false;
            }
        }

        // Load students for this class
        async function loadStudents() {
            try {
                const q = query(
                    collection(db, "users"),
                    where("courseClass", "==", CLASS_ID)
                );
                const querySnapshot = await getDocs(q);

                const students = [];
                querySnapshot.forEach((doc) => {
                    const userData = doc.data();
                    if (userData.fullName && userData.fullName.trim() !== '') {
                        students.push({
                            id: doc.id,
                            userId: doc.id,  // Add userId for consistency
                            name: userData.fullName,
                            avatar: userData.avatar || '../../../../assets/images/avatars/avatar_boy_1.png'
                        });
                    }
                });

                // Update student count
                document.getElementById('studentCount').textContent = `${students.length}/10 học viên`;

                // Display student avatars
                const avatarsContainer = document.getElementById('studentAvatars');
                if (students.length > 0) {
                    avatarsContainer.innerHTML = students.map(student => `
                        <div class="student-avatar" data-name="${student.name}" onclick="viewStudentProfile('${student.userId || student.id}')">
                            <img src="${student.avatar}" alt="${student.name}">
                        </div>
                    `).join('');
                } else {
                    avatarsContainer.innerHTML = '<p style="color: #666; font-style: italic;">Chưa có học viên nào</p>';
                }
            } catch (error) {
                console.error("Error loading students:", error);
                document.getElementById('studentCount').textContent = 'Lỗi tải dữ liệu';
            }
        }

        // View student profile
        window.viewStudentProfile = function(userId) {
            console.log("Navigating to student profile with userId:", userId);
            window.location.href = `../../../auth/student-profile.html?userId=${userId}`;
        };

        // Auth state change listener
        onAuthStateChanged(auth, async (user) => {
            const accessMessage = document.getElementById('accessMessage');
            const classContent = document.getElementById('classContent');
            const lessonsSection = document.getElementById('lessonsSection');

            if (user) {
                const hasAccess = await checkAccess(user);

                if (hasAccess) {
                    accessMessage.style.display = 'none';
                    classContent.style.display = 'block';
                    lessonsSection.style.display = 'block';

                    // Load students
                    await loadStudents();
                } else {
                    accessMessage.innerHTML = `
                        <div class="access-denied">
                            <h3><i class="fas fa-lock"></i> Không có quyền truy cập</h3>
                            <p>Bạn hiện không có quyền truy cập lớp học này!</p>
                            <p>Vui lòng liên hệ giáo viên để được phân lớp hoặc kiểm tra lại thông tin lớp học đã chọn trong tài khoản.</p>
                        </div>
                    `;
                    accessMessage.style.display = 'block';
                    classContent.style.display = 'none';
                    lessonsSection.style.display = 'none';
                }
            } else {
                accessMessage.innerHTML = `
                    <div class="access-denied">
                        <h3><i class="fas fa-sign-in-alt"></i> Vui lòng đăng nhập</h3>
                        <p>Bạn cần đăng nhập để truy cập lớp học này.</p>
                        <a href="../../../auth/" class="meet-link">
                            <i class="fas fa-sign-in-alt"></i> Đăng nhập ngay
                        </a>
                    </div>
                `;
                accessMessage.style.display = 'block';
                classContent.style.display = 'none';
                lessonsSection.style.display = 'none';
            }
        });

        // Lesson navigation functions
        window.showLesson = function(lessonNumber) {
            // Hide all lesson contents
            document.querySelectorAll('.lesson-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.lesson-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected lesson content
            document.getElementById(`lesson${lessonNumber}`).classList.add('active');

            // Add active class to selected tab
            document.querySelectorAll('.lesson-tab')[lessonNumber - 1].classList.add('active');
        };

        // Initialize lesson status
        function initializeLessonStatus() {
            // For now, all lessons are available for practice
            const lessons = ['lesson1Status', 'lesson2Status', 'lesson3Status', 'lesson4Status', 'lesson5Status', 'lesson6Status', 'lesson7Status'];
            lessons.forEach(lessonId => {
                const statusElement = document.getElementById(lessonId);
                if (statusElement) {
                    statusElement.innerHTML = '<span style="color: #4caf50;">Sẵn sàng thực hành</span>';
                }
            });
        }

        // Call initialize function when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeLessonStatus();
        });
    </script>

    <script src="../../../assets/js/script.js"></script>
</body>
</html>
