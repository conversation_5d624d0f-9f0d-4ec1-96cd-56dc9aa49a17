<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bài Tập Code 3: <PERSON><PERSON>, <PERSON><PERSON><PERSON> và Chuỗi - Python C</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../../../../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../../../../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../../../../assets/images/favicon.png">

    <link rel="stylesheet" href="../../../../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Dark Background with Starfield Effect */
        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Shooting stars animation */
        .shooting-star {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #FFD700;
            border-radius: 50%;
            box-shadow: 0 0 10px #FFD700;
            animation: shoot 3s linear infinite;
        }

        @keyframes shoot {
            0% {
                transform: translateX(-100px) translateY(100px);
                opacity: 1;
            }
            100% {
                transform: translateX(100vw) translateY(-100vh);
                opacity: 0;
            }
        }

        /* Floating particles */
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 215, 0, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .assignment-container {
            max-width: 1200px;
            margin: 120px auto 50px;
            padding: 0 20px;
            position: relative;
            z-index: 1;
        }

        .assignment-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            color: white;
            border-radius: 15px;
        }

        .assignment-header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #FFD700;
        }

        .coding-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .problems-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
            padding: 20px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .code-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
            padding: 20px;
        }
        
        .problem-tab {
            display: inline-block;
            padding: 8px 16px;
            margin: 3px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 215, 0, 0.3);
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
            font-size: 0.9rem;
            color: white;
        }

        .problem-tab.active {
            background: #FFD700;
            color: #1a1a2e;
            border-color: #FFD700;
        }

        .problem-tab.completed {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        .problem-tab.skipped {
            background: #6c757d;
            color: white;
            border-color: #6c757d;
        }

        .problem-content {
            display: none;
            margin-top: 20px;
        }

        .problem-content.active {
            display: block;
        }

        .problem-title {
            color: #FFD700;
            font-size: 1.2rem;
            margin-bottom: 15px;
        }

        .problem-description {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            line-height: 1.6;
            color: white;
        }

        .expected-output {
            background: rgba(40, 167, 69, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border-left: 4px solid #28a745;
        }

        .expected-output h4 {
            color: #28a745;
            margin-bottom: 10px;
        }
        
        .code-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
        }
        
        .code-editor {
            width: 100%;
            height: 300px;
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            background: rgba(0, 0, 0, 0.3);
            color: white;
        }

        .code-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }

        .btn-run {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            color: #1a1a2e;
        }

        .btn-run:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .btn-submit {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        .btn-reset {
            background: #6c757d;
            color: white;
        }

        .btn-reset:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .btn-skip {
            background: #ffc107;
            color: #212529;
        }

        .btn-skip:hover {
            background: #e0a800;
            transform: translateY(-2px);
        }

        .info-panel {
            margin-top: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .info-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #FFD700;
        }

        .info-content {
            color: white;
            line-height: 1.6;
        }

        .output-panel {
            margin-top: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .output-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: white;
        }

        .output-content {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            min-height: 100px;
        }

        .result-panel {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }

        .result-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .result-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .progress-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #FFD700, #28a745);
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .back-link {
            display: inline-block;
            color: #FFD700;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .back-link:hover {
            text-decoration: underline;
            color: #FFA500;
        }

        .difficulty {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .difficulty.easy {
            background: #d4edda;
            color: #155724;
        }

        .difficulty.medium {
            background: #fff3cd;
            color: #856404;
        }

        .difficulty.hard {
            background: #f8d7da;
            color: #721c24;
        }

        .hint-section {
            background: rgba(255, 193, 7, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border-left: 4px solid #ffc107;
        }

        .hint-section h4 {
            color: #ffc107;
            margin-bottom: 10px;
        }

        .hint-section p {
            color: rgba(255, 255, 255, 0.9);
        }
        
        @media (max-width: 768px) {
            .coding-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Shooting stars -->
    <div class="shooting-star" style="top: 20%; animation-delay: 0s;"></div>
    <div class="shooting-star" style="top: 40%; animation-delay: 2s;"></div>
    <div class="shooting-star" style="top: 60%; animation-delay: 4s;"></div>
    <div class="shooting-star" style="top: 80%; animation-delay: 6s;"></div>

    <!-- Floating particles -->
    <div class="particle" style="top: 10%; left: 10%; animation-delay: 0s;"></div>
    <div class="particle" style="top: 20%; left: 80%; animation-delay: 1s;"></div>
    <div class="particle" style="top: 70%; left: 20%; animation-delay: 2s;"></div>
    <div class="particle" style="top: 80%; left: 70%; animation-delay: 3s;"></div>

    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../../../index.html">Trang Chủ</a></li>
                    <li><a href="../../../index.html">Lớp Học</a></li>
                    <li><a href="../../../../achievements/">Thành Tích</a></li>
                    <li><a href="../../../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../../../research/">Sự kiện</a></li>
                    <li><a href="../../../../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="assignment-container">
        <a href="../python-c.html" class="back-link">
            <i class="fas fa-arrow-left"></i> Quay lại lớp Python - C
        </a>

        <div class="assignment-header">
            <h1>Bài Tập Code 3: Dữ Liệu – Biến, Kiểu Số và Chuỗi</h1>
            <p>17 bài tập lập trình - Thực hành với biến, số và chuỗi (Không tính điểm)</p>
        </div>

        <!-- Progress Panel -->
        <div class="progress-panel">
            <h3>Tiến độ hoàn thành</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
            <p><span id="completedCount">0 nộp, 0 bỏ qua</span> / 17 bài</p>
        </div>

        <!-- Coding Container -->
        <div class="coding-container">
            <!-- Problems Panel -->
            <div class="problems-panel">
                <h3>Danh sách bài tập</h3>

                <!-- Problem Tabs -->
                <div class="problem-tabs">
                    <div class="problem-tab active" onclick="showProblem(1)" id="tab1">Bài 1</div>
                    <div class="problem-tab" onclick="showProblem(2)" id="tab2">Bài 2</div>
                    <div class="problem-tab" onclick="showProblem(3)" id="tab3">Bài 3</div>
                    <div class="problem-tab" onclick="showProblem(4)" id="tab4">Bài 4</div>
                    <div class="problem-tab" onclick="showProblem(5)" id="tab5">Bài 5</div>
                    <div class="problem-tab" onclick="showProblem(6)" id="tab6">Bài 6</div>
                    <div class="problem-tab" onclick="showProblem(7)" id="tab7">Bài 7</div>
                    <div class="problem-tab" onclick="showProblem(8)" id="tab8">Bài 8</div>
                    <div class="problem-tab" onclick="showProblem(9)" id="tab9">Bài 9</div>
                    <div class="problem-tab" onclick="showProblem(10)" id="tab10">Bài 10</div>
                    <div class="problem-tab" onclick="showProblem(11)" id="tab11">Bài 11</div>
                    <div class="problem-tab" onclick="showProblem(12)" id="tab12">Bài 12</div>
                    <div class="problem-tab" onclick="showProblem(13)" id="tab13">Bài 13</div>
                    <div class="problem-tab" onclick="showProblem(14)" id="tab14">Bài 14</div>
                    <div class="problem-tab" onclick="showProblem(15)" id="tab15">Bài 15</div>
                    <div class="problem-tab" onclick="showProblem(16)" id="tab16">Bài 16</div>
                    <div class="problem-tab" onclick="showProblem(17)" id="tab17">Bài 17</div>
                </div>

                <!-- Problem 1 -->
                <div class="problem-content active" id="problem1">
                    <div class="problem-title">Bài 1: Khai Báo Tuổi <span class="difficulty easy">Dễ</span></div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> khai_bao_tuoi.py<br>
                        <strong>Yêu cầu:</strong> Tạo một biến tên là my_age và gán cho nó giá trị là tuổi hiện tại của bạn (một số nguyên). Sau đó, in giá trị của biến my_age ra màn hình.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi (Ví dụ nếu tuổi là 15):</h4>
                        <div class="code-output">15</div>
                    </div>
                </div>

                <!-- Problem 2 -->
                <div class="problem-content" id="problem2">
                    <div class="problem-title">Bài 2: Lời Chào Với Tên <span class="difficulty easy">Dễ</span></div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> chao_ten.py<br>
                        <strong>Yêu cầu:</strong> Tạo một biến user_name và gán cho nó một tên bất kỳ (ví dụ: "Linh"). Sau đó, in ra màn hình lời chào: "Chao ban, [user_name]!" (thay [user_name] bằng giá trị của biến).
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi (Ví dụ nếu user_name là "Linh"):</h4>
                        <div class="code-output">Chao ban, Linh!</div>
                    </div>
                </div>

                <!-- Problem 3 -->
                <div class="problem-content" id="problem3">
                    <div class="problem-title">Bài 3: Tính Diện Tích Hình Chữ Nhật Đơn Giản <span class="difficulty easy">Dễ</span></div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> dien_tich_hcn.py<br>
                        <strong>Yêu cầu:</strong><br>
                        • Tạo biến chieu_dai và gán giá trị là 10.<br>
                        • Tạo biến chieu_rong và gán giá trị là 5.<br>
                        • Tạo biến dien_tich và gán cho nó kết quả của chieu_dai nhân chieu_rong (dấu nhân: *).<br>
                        • In giá trị của biến dien_tich ra màn hình.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">50</div>
                    </div>
                </div>

                <!-- Problem 4 -->
                <div class="problem-content" id="problem4">
                    <div class="problem-title">Bài 4: Lặp Lại Lời Cổ Vũ <span class="difficulty easy">Dễ</span></div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> co_vu.py<br>
                        <strong>Yêu cầu:</strong> Tạo một biến slogan có giá trị là "Python Co Len! ". In biến slogan ra màn hình 3 lần liên tiếp trên cùng một dòng.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">Python Co Len! Python Co Len! Python Co Len! </div>
                    </div>
                </div>

                <!-- Problem 5 -->
                <div class="problem-content" id="problem5">
                    <div class="problem-title">Bài 5: Kiểm Tra Kiểu Dữ Liệu <span class="difficulty easy">Dễ</span></div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> kiem_tra_kieu.py<br>
                        <strong>Yêu cầu:</strong><br>
                        • Tạo biến var1 gán giá trị 100.<br>
                        • Tạo biến var2 gán giá trị 9.99.<br>
                        • Tạo biến var3 gán giá trị "Hello Python".<br>
                        • In ra kiểu dữ liệu của từng biến, mỗi kiểu trên một dòng.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">&lt;class 'int'&gt;<br>&lt;class 'float'&gt;<br>&lt;class 'str'&gt;</div>
                    </div>
                </div>

                <!-- Problem 6 -->
                <div class="problem-content" id="problem6">
                    <div class="problem-title">Bài 6: Thông Tin Sản Phẩm <span class="difficulty medium">Trung bình</span></div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> san_pham.py<br>
                        <strong>Yêu cầu:</strong><br>
                        • Tạo biến ten_san_pham gán giá trị "Ao Thun Python".<br>
                        • Tạo biến gia_ban gán giá trị 250.0 (số thực).<br>
                        • Tạo biến so_luong_ton_kho gán giá trị 50.<br>
                        • In thông tin sản phẩm ra màn hình theo định dạng sau:
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">Ten san pham: Ao Thun Python<br>Gia ban: 250.0 VND<br>So luong ton kho: 50</div>
                    </div>
                    <div class="hint-section">
                        <h4>💡 Gợi ý:</h4>
                        <p>Nếu chỉ dùng print và nối chuỗi, bạn cần in gia_ban và so_luong_ton_kho riêng hoặc tìm cách nối chúng với chuỗi một cách khéo léo, ví dụ print("Gia ban: ", gia_ban, " VND") nếu đã học print nhiều tham số.</p>
                    </div>
                </div>

                <!-- Problem 7 -->
                <div class="problem-content" id="problem7">
                    <div class="problem-title">Bài 7: Tên Viết Tắt <span class="difficulty medium">Trung bình</span></div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> ten_viet_tat.py<br>
                        <strong>Yêu cầu:</strong><br>
                        • Tạo biến ho_dem gán giá trị "Nguyen Van".<br>
                        • Tạo biến ten gán giá trị "An".<br>
                        • Lấy ký tự đầu tiên của ho_dem và ký tự đầu tiên của ten.<br>
                        • Nối chúng lại với dấu chấm ở giữa để tạo tên viết tắt. In tên viết tắt đó ra màn hình.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">N.A</div>
                    </div>
                </div>

                <!-- Problem 8 -->
                <div class="problem-content" id="problem8">
                    <div class="problem-title">Bài 8: Đảo Ngược Chuỗi Đơn Giản <span class="difficulty easy">Dễ</span></div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> dao_nguoc_chuoi.py<br>
                        <strong>Yêu cầu:</strong> Cho biến s = "Hello". Sử dụng slicing để đảo ngược chuỗi s và in kết quả ra màn hình.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">olleH</div>
                    </div>
                </div>

                <!-- Problem 9 -->
                <div class="problem-content" id="problem9">
                    <div class="problem-title">Bài 9: Tạo Username <span class="difficulty medium">Trung bình</span></div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> tao_username.py<br>
                        <strong>Yêu cầu:</strong><br>
                        • Tạo biến first_name gán giá trị "taylor".<br>
                        • Tạo biến last_name gán giá trị "swift".<br>
                        • Tạo biến birth_year gán giá trị "1989".<br>
                        • Tạo username bằng cách nối 3 ký tự đầu của first_name (chuyển thành chữ thường), 3 ký tự đầu của last_name (chuyển thành chữ thường), và 2 ký tự cuối của birth_year.<br>
                        • In username ra màn hình.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">tayswi89</div>
                    </div>
                </div>

                <!-- Problem 10 -->
                <div class="problem-content" id="problem10">
                    <div class="problem-title">Bài 10: Định Dạng Lại Tên <span class="difficulty medium">Trung bình</span></div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> dinh_dang_ten.py<br>
                        <strong>Yêu cầu:</strong><br>
                        • Tạo biến full_name có giá trị là " ngUYen vAN binh ".<br>
                        • Sử dụng các phương thức chuỗi để:<br>
                        • Loại bỏ khoảng trắng thừa ở đầu và cuối.<br>
                        • Chuyển toàn bộ tên thành chữ thường.<br>
                        • Sau đó, chuyển ký tự đầu tiên của mỗi từ (sau khi đã xử lý ở trên) thành chữ hoa<br>
                        • In tên đã định dạng ra màn hình.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">Nguyen Van Binh</div>
                    </div>
                    <div class="hint-section">
                        <h4>💡 Gợi ý:</h4>
                        <p>Nếu chưa học title(), bạn có thể làm như sau: strip(), lower(), sau đó lấy name[0].upper() + name[1:] cho từng từ sau khi tách chuỗi, hoặc làm phức tạp hơn bằng cách tìm khoảng trắng để xác định đầu từ.</p>
                    </div>
                </div>

                <!-- Problem 11 -->
                <div class="problem-content" id="problem11">
                    <div class="problem-title">Bài 11: Tạo Email Đơn Giản <span class="difficulty medium">Trung bình</span></div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> tao_email.py<br>
                        <strong>Yêu cầu:</strong><br>
                        • Tạo biến ho_va_ten gán giá trị "Tran Minh Hoang".<br>
                        • Tạo biến ten_mien_cong_ty gán giá trị "techcompany.com".<br>
                        • Tạo địa chỉ email theo quy tắc: lấy chữ cái đầu tiên của mỗi từ trong ho_va_ten (chuyển thành chữ thường), nối chúng lại, sau đó nối với @ và ten_mien_cong_ty.<br>
                        • In địa chỉ email ra màn hình.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output"><EMAIL></div>
                    </div>
                    <div class="hint-section">
                        <h4>💡 Gợi ý:</h4>
                        <p>Học sinh cần dùng indexing và nối chuỗi. Có thể giả sử tên có 3 từ để đơn giản hóa việc lấy ký tự đầu.</p>
                    </div>
                </div>

                <!-- Problem 12 -->
                <div class="problem-content" id="problem12">
                    <div class="problem-title">Bài 12: Trích Xuất Thông Tin Từ Chuỗi <span class="difficulty hard">Khó</span></div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> trich_xuat_thong_tin.py<br>
                        <strong>Yêu cầu:</strong> Cho biến log_entry = "INFO:UserID:12345:Action:ViewPage".<br>
                        • Sử dụng slicing và/hoặc find() để trích xuất UserID (là "12345").<br>
                        • Sử dụng slicing và/hoặc find() để trích xuất Action (là "ViewPage").<br>
                        • In UserID và Action ra màn hình, mỗi thông tin trên một dòng.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">UserID: 12345<br>Action: ViewPage</div>
                    </div>
                    <div class="hint-section">
                        <h4>💡 Gợi ý:</h4>
                        <p>Đây là bài khó, học sinh cần tìm vị trí của các dấu : để cắt chuỗi.</p>
                    </div>
                </div>

                <!-- Problem 13 -->
                <div class="problem-content" id="problem13">
                    <div class="problem-title">Bài 13: Tạo Khung Văn Bản <span class="difficulty medium">Trung bình</span></div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> khung_van_ban.py<br>
                        <strong>Yêu cầu:</strong><br>
                        • Tạo biến message gán giá trị "Python is Awesome".<br>
                        • In ra một khung bao quanh message bằng các ký tự * và - như sau:
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">********************<br>* Python is Awesome *<br>********************</div>
                    </div>
                    <div class="hint-section">
                        <h4>💡 Gợi ý:</h4>
                        <p>Học sinh cần tính độ dài của message, sau đó tạo các dòng * có độ dài phù hợp. Dòng giữa cần có *, khoảng trắng, message, khoảng trắng, *.</p>
                    </div>
                </div>

                <!-- Problem 14 -->
                <div class="problem-content" id="problem14">
                    <div class="problem-title">Bài 14: Mã Hóa Caesar Đơn Giản (Dịch Vòng) <span class="difficulty hard">Khó</span></div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> ma_hoa_caesar.py<br>
                        <strong>Yêu cầu:</strong><br>
                        • Tạo biến plain_text gán giá trị "HELLO".<br>
                        • Tạo biến shift gán giá trị 3.<br>
                        • Với mỗi ký tự trong plain_text, dịch chuyển nó đi shift vị trí trong bảng chữ cái (ví dụ: A dịch 3 thành D, B dịch 3 thành E,... Z dịch 3 thành C).<br>
                        • Chỉ xử lý chữ cái IN HOA, các ký tự khác giữ nguyên (cho bài này, giả sử chuỗi đầu vào chỉ có chữ IN HOA).<br>
                        • In ra chuỗi đã mã hóa.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">KHOOR</div>
                    </div>
                    <div class="hint-section">
                        <h4>💡 Gợi ý:</h4>
                        <p>Bài này rất khó nếu chỉ dùng kiến thức Bài 3. Học sinh có thể tạo một chuỗi alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ". Với mỗi ký tự trong plain_text, tìm vị trí của nó trong alphabet, cộng với shift, rồi lấy ký tự ở vị trí mới (có xử lý vòng lặp nếu vượt quá Z).</p>
                    </div>
                </div>

                <!-- Problem 15 -->
                <div class="problem-content" id="problem15">
                    <div class="problem-title">Bài 15: Tạo Mật Khẩu Ngẫu Nhiên Giả (Fixed) <span class="difficulty medium">Trung bình</span></div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> mat_khau_gia.py<br>
                        <strong>Yêu cầu:</strong><br>
                        • Tạo biến name_part gán giá trị "User".<br>
                        • Tạo biến number_part gán giá trị "2024". (Chuỗi)<br>
                        • Tạo biến symbol_part gán giá trị "@#!".<br>
                        • Tạo mật khẩu bằng cách: lấy 2 ký tự đầu của name_part + number_part + ký tự thứ hai của symbol_part + 3 ký tự cuối của name_part đảo ngược.<br>
                        • In mật khẩu ra màn hình.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi (Với "User" và "2024"):</h4>
                        <div class="code-output">Us2024#res</div>
                    </div>
                    <div class="hint-section">
                        <h4>💡 Gợi ý:</h4>
                        <p>Để học sinh làm được với kiến thức Bài 3: Lấy 2 ký tự đầu name_part[0:2]. Ký tự thứ hai của symbol_part là symbol_part[1]. 3 ký tự cuối của name_part đảo ngược: name_part[3] + name_part[2] + name_part[1] nếu name_part là "User".</p>
                    </div>
                </div>

                <!-- Problem 16 -->
                <div class="problem-content" id="problem16">
                    <div class="problem-title">Bài 16: Format Địa Chỉ <span class="difficulty easy">Dễ</span></div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> format_dia_chi.py<br>
                        <strong>Yêu cầu:</strong><br>
                        • Tạo biến so_nha gán giá trị "123B".<br>
                        • Tạo biến ten_duong gán giá trị "Duong Tran Hung Dao".<br>
                        • Tạo biến phuong gán giá trị "Phuong Pham Ngu Lao".<br>
                        • Tạo biến quan gán giá trị "Quan 1".<br>
                        • Tạo biến thanh_pho gán giá trị "TP. Ho Chi Minh".<br>
                        • In ra địa chỉ đầy đủ trên một dòng, mỗi phần cách nhau bởi dấu phẩy và một khoảng trắng.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">123B, Duong Tran Hung Dao, Phuong Pham Ngu Lao, Quan 1, TP. Ho Chi Minh</div>
                    </div>
                </div>

                <!-- Problem 17 -->
                <div class="problem-content" id="problem17">
                    <div class="problem-title">Bài 17: Phân Tích Chuỗi Đơn Giản <span class="difficulty hard">Khó</span></div>
                    <div class="problem-description">
                        <strong>Tên file:</strong> phan_tich_chuoi.py<br>
                        <strong>Yêu cầu:</strong><br>
                        • Tạo biến my_string gán giá trị "Python:123:Data:45.6".<br>
                        • Tìm vị trí của dấu hai chấm (:) đầu tiên.<br>
                        • Tìm vị trí của dấu hai chấm (:) thứ hai (tính từ sau vị trí đầu tiên).<br>
                        • Tìm vị trí của dấu hai chấm (:) cuối cùng.<br>
                        • In ra ba vị trí này, mỗi vị trí trên một dòng.
                    </div>
                    <div class="expected-output">
                        <h4><i class="fas fa-terminal"></i> Output mong đợi:</h4>
                        <div class="code-output">Vi tri dau hai cham dau tien: 6<br>Vi tri dau hai cham thu hai: 10<br>Vi tri dau hai cham cuoi cung: 15</div>
                    </div>
                    <div class="hint-section">
                        <h4>💡 Gợi ý:</h4>
                        <p>Phương thức find() có tham số start để bắt đầu tìm kiếm từ một vị trí nhất định. Ví dụ: my_string.find(":", vi_tri_dau_tien + 1)</p>
                    </div>
                </div>
            </div>

            <!-- Code Panel -->
            <div class="code-panel">
                <h3>Trình soạn thảo code</h3>
                <textarea class="code-editor" id="codeEditor" placeholder="# Viết code Python của bạn ở đây..."></textarea>

                <div class="code-actions">
                    <button class="btn btn-submit" disabled style="opacity: 0.5; cursor: not-allowed;">
                        <i class="fas fa-ban"></i> Tạm Ngưng Nộp Bài
                    </button>
                    <button class="btn btn-skip" onclick="skipProblem()">
                        <i class="fas fa-forward"></i> Bỏ Qua
                    </button>
                    <button class="btn btn-reset" onclick="resetCode()">
                        <i class="fas fa-undo"></i> Reset
                    </button>
                </div>

                <!-- Info Panel -->
                <div class="info-panel">
                    <div class="info-title"><i class="fas fa-info-circle"></i> Lưu ý:</div>
                    <div class="info-content">
                        • Đây là bài tập luyện tập, không tính điểm<br>
                        • Bạn có thể xem output mong đợi để tham khảo<br>
                        • Nếu không biết làm, hãy bấm "Bỏ Qua"<br>
                        • Hãy thử suy nghĩ và code theo cách của bạn
                    </div>
                </div>

                <!-- Result Panel -->
                <div class="result-panel" id="resultPanel">
                    <div id="resultContent"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../../../../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Global variables
        let currentProblem = 1;
        let completedProblems = new Set();
        let skippedProblems = new Set();
        const totalProblems = 17;

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            showProblem(1);
            updateProgress();
        });

        // Show specific problem
        function showProblem(problemNumber) {
            // Hide all problem contents
            for (let i = 1; i <= totalProblems; i++) {
                const content = document.getElementById(`problem${i}`);
                const tab = document.getElementById(`tab${i}`);
                if (content) content.classList.remove('active');
                if (tab) tab.classList.remove('active');
            }

            // Show selected problem
            const selectedContent = document.getElementById(`problem${problemNumber}`);
            const selectedTab = document.getElementById(`tab${problemNumber}`);
            if (selectedContent) selectedContent.classList.add('active');
            if (selectedTab) selectedTab.classList.add('active');

            currentProblem = problemNumber;

            // Clear code editor for new problem
            document.getElementById('codeEditor').value = '';

            // Hide result panel
            document.getElementById('resultPanel').style.display = 'none';
        }

        // Submit code for current problem
        function submitCode() {
            const code = document.getElementById('codeEditor').value.trim();

            if (!code) {
                showResult('error', 'Vui lòng nhập code trước khi nộp bài!');
                return;
            }

            // Mark as completed
            completedProblems.add(currentProblem);
            skippedProblems.delete(currentProblem);

            // Update tab appearance
            const tab = document.getElementById(`tab${currentProblem}`);
            if (tab) {
                tab.classList.remove('skipped');
                tab.classList.add('completed');
            }

            showResult('success', `Đã nộp bài ${currentProblem} thành công! Code của bạn đã được lưu.`);
            updateProgress();

            // Auto move to next problem after 2 seconds
            setTimeout(() => {
                if (currentProblem < totalProblems) {
                    showProblem(currentProblem + 1);
                }
            }, 2000);
        }

        // Skip current problem
        function skipProblem() {
            if (confirm(`Bạn có chắc chắn muốn bỏ qua bài ${currentProblem}?`)) {
                // Mark as skipped
                skippedProblems.add(currentProblem);
                completedProblems.delete(currentProblem);

                // Update tab appearance
                const tab = document.getElementById(`tab${currentProblem}`);
                if (tab) {
                    tab.classList.remove('completed');
                    tab.classList.add('skipped');
                }

                showResult('error', `Đã bỏ qua bài ${currentProblem}.`);
                updateProgress();

                // Auto move to next problem after 1 second
                setTimeout(() => {
                    if (currentProblem < totalProblems) {
                        showProblem(currentProblem + 1);
                    }
                }, 1000);
            }
        }

        // Reset code editor
        function resetCode() {
            if (confirm('Bạn có chắc chắn muốn xóa toàn bộ code đã viết?')) {
                document.getElementById('codeEditor').value = '';
                document.getElementById('resultPanel').style.display = 'none';
            }
        }

        // Show result message
        function showResult(type, message) {
            const resultPanel = document.getElementById('resultPanel');
            const resultContent = document.getElementById('resultContent');

            resultPanel.className = `result-panel result-${type}`;
            resultContent.textContent = message;
            resultPanel.style.display = 'block';
        }

        // Update progress bar and counter
        function updateProgress() {
            const completedCount = completedProblems.size;
            const skippedCount = skippedProblems.size;
            const progressPercentage = ((completedCount + skippedCount) / totalProblems) * 100;

            document.getElementById('progressFill').style.width = `${progressPercentage}%`;
            document.getElementById('completedCount').textContent = `${completedCount} nộp, ${skippedCount} bỏ qua`;
        }

        // Make functions globally available
        window.showProblem = showProblem;
        window.submitCode = submitCode;
        window.skipProblem = skipProblem;
        window.resetCode = resetCode;
    </script>

    <script>
        // Create more shooting stars dynamically
        function createShootingStar() {
            const star = document.createElement('div');
            star.className = 'shooting-star';
            star.style.top = Math.random() * 100 + '%';
            star.style.animationDelay = Math.random() * 3 + 's';
            document.body.appendChild(star);

            setTimeout(() => {
                star.remove();
            }, 3000);
        }

        // Create shooting stars periodically
        setInterval(createShootingStar, 2000);

        // Create floating particles
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.top = Math.random() * 100 + '%';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 6 + 's';
            document.body.appendChild(particle);

            setTimeout(() => {
                particle.remove();
            }, 6000);
        }

        // Create particles periodically
        setInterval(createParticle, 1000);
    </script>
</body>
</html>
